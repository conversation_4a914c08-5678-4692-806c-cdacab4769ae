import React, { useEffect, useState, useRef } from "react";
import { Link } from "react-router-dom";
import { Container, Row, Col, Card, Button, Badge } from "react-bootstrap"; // Removed Tabs and Tab
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

// The data object remains the same, no changes needed here.
const testingPageData = {
  hero: {
    title: "Excellence in Quality Engineering",
    subtitle:
      "We help businesses thrive by ensuring their software is flawless and high-performing. Our end-to-end testing and quality assurance services cover every stage of development. From functionality to performance, we ensure reliability at every level.s",
    backgroundImage:
      "https://images.unsplash.com/photo-1581093458791-9f3c3900eebd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
  },
  intro: {
    title: "Pioneering Comprehensive Testing Solutions",
    description:
      "At Makonis, we specialize in elevating your software's integrity. By integrating advanced Software Test Engineering, Accessibility Assurance, and Product Engineering, we forge transparent, value-driven partnerships. Our seasoned experts deliver innovative solutions across diverse tech domains, ensuring you achieve your business goals with confidence.",
    image:
      "https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
  },
  services: [
    {
      id: "functional",
      title: "Functional Testing",
      icon: "fa-check-circle",
      description:
        "We verify and validate every function of your software to ensure it operates in conformance with your exact specifications. Our meticulous approach guarantees a seamless user experience.",
      features: [
        "Manual & Exploratory Testing",
        "Regression Testing",
        "Integration Testing",
        "User Acceptance Testing (UAT)",
        "Compatibility Testing",
      ],
      image:
        "https://images.unsplash.com/photo-1599658880436-c61792e70672?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      color: "#",
    },
    {
      id: "automation",
      title: "Test Automation",
      icon: "fa-robot",
      description:
        "Accelerate your release cycles and improve accuracy with our Test Automation services. We build robust, scalable automation frameworks tailored to your product's needs.",
      features: [
        "Selenium & Cypress Frameworks",
        "Appium for Mobile Automation",
        "API & Microservices Automation",
        "CI/CD Pipeline Integration",
        "BDD with Cucumber",
      ],
      image:
        "https://images.unsplash.com/photo-1610563166126-a8537b3b4b5b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      color: "#C850C0",
    },
    {
      id: "performance",
      title: "Performance Testing",
      icon: "fa-tachometer-alt",
      description:
        "We ensure your application is responsive, reliable, and scalable under any load condition. Pinpoint and eliminate performance bottlenecks before they impact your users.",
      features: [
        "Load & Stress Testing",
        "Endurance & Scalability Testing",
        "Capacity Planning",
        "Bottleneck Analysis",
        "Real-time Performance Monitoring",
      ],
      image:
        "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      color: "#43cea2",
    },
    {
      id: "security",
      title: "Security Testing",
      icon: "fa-shield-alt",
      description:
        "Protect your application from modern threats. Our security testing services proactively identify vulnerabilities and ensure your data and users are secure.",
      features: [
        "Vulnerability Assessment",
        "Penetration Testing (Pentesting)",
        "Static & Dynamic Analysis (SAST/DAST)",
        "Compliance Audits (GDPR, HIPAA)",
        "Risk-Based Security Hardening",
      ],
      image:
        "https://images.unsplash.com/photo-1555949963-ff9fe0c870eb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      color: "#F37335",
    },
  ],
  process: {
    title: "Our Structured Testing Lifecycle",
    description:
      "We follow a proven, systematic approach to ensure maximum test coverage and deliver quantifiable quality improvements for your applications.",
    steps: [
      {
        number: "01",
        title: "Discovery & Strategy",
        description:
          "We dive deep into your requirements and architecture to define a tailored, risk-based test strategy.",
        icon: "fa-search",
      },
      {
        number: "02",
        title: "Test Planning & Design",
        description:
          "Our team architects comprehensive test plans, crafts detailed test cases, and prepares the optimal test environment.",
        icon: "fa-clipboard-list",
      },
      {
        number: "03",
        title: "Execution & Automation",
        description:
          "We execute tests meticulously, automate where it adds value, and rapidly identify and document defects.",
        icon: "fa-cogs",
      },
      {
        number: "04",
        title: "Analysis & Reporting",
        description:
          "We track defects to resolution and provide clear, actionable reports with key metrics and quality insights.",
        icon: "fa-chart-pie",
      },
      {
        number: "05",
        title: "Optimization & Delivery",
        description:
          "We help you release with confidence, providing feedback for continuous process improvement.",
        icon: "fa-rocket",
      },
    ],
  },
  tools: {
    title: "Our Arsenal of Tools & Technologies",
    description:
      "We utilize a modern, comprehensive tech stack to deliver efficient and effective testing services across all domains.",
    categories: [
      {
        name: "Automation",
        tools: [
          "Selenium",
          "Appium",
          "Cypress",
          "Playwright",
          "Katalon Studio",
          "Robot Framework",
        ],
        icon: "fa-robot",
      },
      {
        name: "Performance",
        tools: [
          "JMeter",
          "LoadRunner",
          "Gatling",
          "K6",
          "BlazeMeter",
          "NeoLoad",
        ],
        icon: "fa-tachometer-alt",
      },
      {
        name: "API Testing",
        tools: [
          "Postman",
          "SoapUI",
          "REST Assured",
          "Karate DSL",
          "ReadyAPI",
          "Pact",
        ],
        icon: "fa-code-branch",
      },
      {
        name: "DevOps & CI/CD",
        tools: [
          "Jenkins",
          "Docker",
          "Kubernetes",
          "Azure DevOps",
          "GitLab CI",
          "CircleCI",
        ],
        icon: "fa-cogs",
      },
    ],
  },
  stats: [
    { value: "500+", label: "Successful Projects", icon: "fa-project-diagram" },
    { value: "99%", label: "Client Satisfaction", icon: "fa-smile-beam" },
    {
      value: "40%",
      label: "Avg. Time-to-Market Reduction",
      icon: "fa-shipping-fast",
    },
    { value: "24/7", label: "Dedicated Support", icon: "fa-headset" },
  ],
  cta: {
    title: "Ready to Elevate Your Software Quality?",
    text: "Partner with us to build robust, reliable, and remarkable applications. Let's discuss how our testing expertise can drive your project's success.",
    buttonText: "Schedule a Consultation",
    buttonLink: "/contact",
  },
};

const TestingPage = () => {
  const [activeService, setActiveService] = useState("functional");
  const heroRef = useRef(null);
  const sectionsRef = useRef([]);

  const primaryColor = "#002956";
  const secondaryColor = "#00a0e9";

  const gradientTextStyle = {
    fontSize: "clamp(2.5rem, 6vw, 3.5rem)",
    lineHeight: "1.2",
    fontWeight: "bold",
    background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent",
    backgroundClip: "text",
    textShadow: "0 0 40px rgba(0, 160, 233, 0.4)",
  };

  const glassCardStyle = {
    background: "rgba(255, 255, 255, 0.05)",
    backdropFilter: "blur(15px)",
    border: "1px solid rgba(255, 255, 255, 0.1)",
    borderRadius: "1rem",
    transition: "all 0.4s ease",
  };

  useEffect(() => {
    window.scrollTo(0, 0);

    const ctx = gsap.context(() => {
      if (heroRef.current) {
        gsap.from(heroRef.current.querySelector("h1"), {
          y: 100,
          opacity: 0,
          duration: 1.2,
          ease: "power3.out",
          delay: 0.2,
        });
        gsap.from(heroRef.current.querySelector("p"), {
          y: 50,
          opacity: 0,
          duration: 1,
          ease: "power2.out",
          delay: 0.5,
        });
        gsap.from(heroRef.current.querySelector(".card-makonis-glass"), {
          y: 50,
          opacity: 0,
          duration: 1.2,
          ease: "power3.out",
          delay: 0.8,
        });
      }

      sectionsRef.current.forEach((section) => {
        const targets = section.querySelectorAll(".animate-in");
        if (targets.length > 0) {
          gsap.from(targets, {
            y: 60,
            opacity: 0,
            duration: 1,
            ease: "power3.out",
            stagger: 0.2,
            scrollTrigger: {
              trigger: section,
              start: "top 85%",
              toggleActions: "play none none reverse",
            },
          });
        }
      });
    });

    return () => ctx.revert();
  }, []);

  const addToRefs = (el) => {
    if (el && !sectionsRef.current.includes(el)) {
      sectionsRef.current.push(el);
    }
  };

  return (
    <div
      className="testing-page"
      style={{ background: "#0f172a", color: "rgba(255, 255, 255, 0.9)" }}
    >
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="testing-hero d-flex align-items-center text-white position-relative overflow-hidden"
        style={{
          background:
            "linear-gradient(135deg, #002956 0%, #001a3a 50%, #000f1f 100%)",
          minHeight: "100vh",
          position: "relative",
        }}
      >
        <div
          className="position-absolute w-100 h-100"
          style={{
            backgroundImage: `url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>')`,
            opacity: 0.3,
            pointerEvents: "none",
          }}
        />
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="position-absolute rounded-circle"
              style={{
                width: `${Math.random() * 100 + 50}px`,
                height: `${Math.random() * 100 + 50}px`,
                background: `rgba(0, 160, 233, ${Math.random() * 0.1 + 0.05})`,
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animation: `float ${
                  Math.random() * 10 + 10
                }s infinite ease-in-out`,
                backdropFilter: "blur(1px)",
              }}
            />
          ))}
        </div>
        <Container className="position-relative" style={{ zIndex: 2 }}>
          <Row className="align-items-center">
            <h1
                className="display-1 fw-bold mb-4 text-center"
                style={{
                  fontSize: "clamp(3rem, 7vw, 4.4rem)",
                  lineHeight: "1.1",
                  background:
                    "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                  textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                }}
              >
                {testingPageData.hero.title}
              </h1>
            <Col lg={7} className="text-white text-center text-lg-start">
              
              <p
                className="mb-5"
                style={{
                  fontSize: "1.25rem",
                  lineHeight: "1.6",
                  color: "rgba(255,255,255,0.8)",
                }}
              >
                {testingPageData.hero.subtitle}
              </p>
            </Col>
            <Col lg={5}>
              <div className="position-relative">
                <div
                  className="card-makonis-glass p-4"
                  style={{
                    background: "rgba(255, 255, 255, 0.08)",
                    backdropFilter: "blur(20px)",
                    border: "1px solid rgba(255, 255, 255, 0.2)",
                    borderRadius: "1rem",
                  }}
                >
                  <img
                    src={testingPageData.hero.backgroundImage}
                    alt="Quality Assurance & Testing"
                    className="w-100 rounded-3"
                    style={{ height: "300px", objectFit: "cover" }}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Intro Section */}
      <section ref={addToRefs} className="py-5 my-5">
        <Container>
          <Row className="align-items-center g-5">
             <h2 className="text-center" style={gradientTextStyle}>{testingPageData.intro.title}</h2>
            <Col lg={7} className="animate-in">
             
              <p className="lead mt-4 mb-4">
                {testingPageData.intro.description}
              </p>
            </Col>
            <Col lg={5} className="animate-in">
              <div className="p-3" style={glassCardStyle}>
                <img
                  src={testingPageData.intro.image}
                  alt="Our Team"
                  className="img-fluid rounded-3"
                  style={{ minHeight: "350px", objectFit: "cover" }}
                />
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Stats Section */}
      <section ref={addToRefs} className="py-5">
        <Container>
          <Row className="g-4">
            {testingPageData.stats.map((stat, index) => (
              <Col md={6} lg={3} key={index} className="animate-in">
                <div
                  className="text-center p-4 h-100 stat-card"
                  style={glassCardStyle}
                >
                  <i
                    className={`fas ${stat.icon} fa-3x mb-3`}
                    style={{ color: secondaryColor }}
                  ></i>
                  <h3 className="display-5 fw-bold text-white">{stat.value}</h3>
                  <p className="mb-0 text-white-50">{stat.label}</p>
                </div>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* Services Section */}
      <section ref={addToRefs} className="py-5 my-5">
        <Container>
          <div className="text-center mb-5 animate-in">
            <h2 style={gradientTextStyle}>
              A Full Spectrum of Testing Services
            </h2>
            <p className="lead mx-auto" style={{ maxWidth: "700px" }}>
              We offer a wide range of testing services to ensure your
              applications meet the highest quality standards.
            </p>
          </div>
          <div className="d-flex justify-content-center flex-wrap gap-2 mb-5 animate-in">
            {testingPageData.services.map((service) => (
              <Button
                key={service.id}
                variant={
                  activeService === service.id ? "primary" : "outline-secondary"
                }
                className="rounded-pill px-4 py-2"
                style={
                  activeService === service.id
                    ? {
                        background: secondaryColor,
                        borderColor: secondaryColor,
                      }
                    : {}
                }
                onClick={() => setActiveService(service.id)}
              >
                <i className={`fas ${service.icon} me-2`}></i>
                {service.title}
              </Button>
            ))}
          </div>
          <div className="mt-4">
            {testingPageData.services.map((service) => (
              <div
                key={service.id}
                className={`${
                  activeService === service.id ? "d-block" : "d-none"
                }`}
              >
                <div className="p-4" style={glassCardStyle}>
                  <Row className="g-5 align-items-center">
                    <Col lg={6}>
                      <img
                        src={service.image}
                        alt={service.title}
                        className="img-fluid rounded-3 w-100"
                        style={{ height: "400px", objectFit: "cover" }}
                      />
                    </Col>
                    <Col lg={6}>
                      <h3
                        className="fw-bold mb-3"
                        style={{ color: service.color }}
                      >
                        {service.title}
                      </h3>
                      <p>{service.description}</p>
                      <ul className="list-unstyled mt-4">
                        {service.features.map((feature, index) => (
                          <li
                            key={index}
                            className="d-flex align-items-center mb-2"
                          >
                            <i
                              className="fas fa-check-circle me-2"
                              style={{ color: service.color }}
                            ></i>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </Col>
                  </Row>
                </div>
              </div>
            ))}
          </div>
        </Container>
      </section>

      {/* Process Section */}
      <section ref={addToRefs} className="py-5 my-5">
        <Container>
          <div className="text-center mb-5 animate-in">
            <h2 style={gradientTextStyle}>{testingPageData.process.title}</h2>
            <p className="lead mx-auto" style={{ maxWidth: "700px" }}>
              {testingPageData.process.description}
            </p>
          </div>
          <div className="process-timeline-new position-relative">
            <div className="timeline-line-new"></div>
            {testingPageData.process.steps.map((step, index) => (
              <div
                key={index}
                className={`timeline-item animate-in ${
                  index % 2 === 0 ? "left" : "right"
                }`}
              >
                <div className="timeline-dot"></div>
                <div className="timeline-content">
                  <div className="timeline-icon">
                    <i className={`fas ${step.icon}`}></i>
                  </div>
                  <h3 className="h5 fw-bold" style={{ color: secondaryColor }}>
                    {step.number}. {step.title}
                  </h3>
                  <p className="mb-0 small">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </Container>
      </section>

      {/* ===== REDESIGNED TOOLS SECTION (CARD-BASED LAYOUT) ===== */}
      <section ref={addToRefs} className="py-5 my-5">
        <Container>
          <div className="text-center mb-5 animate-in">
            <h2 style={gradientTextStyle}>{testingPageData.tools.title}</h2>
            <p className="lead mx-auto" style={{ maxWidth: "700px" }}>
              {testingPageData.tools.description}
            </p>
          </div>

          {/* Tools Categories Grid */}
          <Row className="g-4">
            {testingPageData.tools.categories.map((category, categoryIndex) => (
              <Col lg={3} md={4} sm={6} key={category.name} className="animate-in">
                <div className="tools-category-card h-100">
                  {/* Category Header */}
                  <div className="category-header">
                    <div className="category-icon-wrapper">
                      <i className={`fas ${category.icon}`}></i>
                    </div>
                    <h3 className="category-title">{category.name}</h3>
                  </div>

                  {/* Tools Grid */}
                  <div className="tools-grid">
                    {category.tools.map((tool, toolIndex) => (
                      <div key={toolIndex} className="tool-item">
                        <span className="tool-name">{tool}</span>
                        <div className="tool-shine"></div>
                      </div>
                    ))}
                  </div>

                  {/* Floating Particles Effect */}
                  <div className="floating-particles">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className="particle"
                        style={{
                          animationDelay: `${i * 0.5 + categoryIndex * 0.2}s`,
                        }}
                      />
                    ))}
                  </div>
                </div>
              </Col>
            ))}
          </Row>
        </Container>
      </section>
  

      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(10deg); }
        }


        .stat-card:hover {
          transform: translateY(-8px) scale(1.02);
          box-shadow: 0 20px 40px rgba(0, 160, 233, 0.2);
          border-color: rgba(0, 160, 233, 0.5);
        }
        .cta-button { transition: all 0.3s ease; }
        .cta-button:hover {
          transform: translateY(-5px) scale(1.05);
          box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        /* --- Process Timeline Styles --- */
        .process-timeline-new { width: 100%; max-width: 800px; margin: 0 auto; padding: 2rem 0; }
        .timeline-line-new { position: absolute; left: 50%; top: 0; bottom: 0; width: 4px; background: rgba(255, 255, 255, 0.1); transform: translateX(-50%); border-radius: 2px; }
        .timeline-item { padding: 10px 40px; position: relative; width: 50%; }
        .timeline-item.left { left: 0; padding-right: 50px; }
        .timeline-item.right { left: 50%; padding-left: 50px; }
        .timeline-item::after { content: ''; position: absolute; width: 25px; height: 2px; background: rgba(255, 255, 255, 0.2); top: 30px; }
        .timeline-item.left::after { right: 25px; }
        .timeline-item.right::after { left: 25px; }
        .timeline-dot { content: ''; position: absolute; width: 16px; height: 16px; background-color: ${secondaryColor}; border: 4px solid #0f172a; top: 23px; border-radius: 50%; z-index: 1; box-shadow: 0 0 10px ${secondaryColor}; }
        .timeline-item.left .timeline-dot { right: -8px; }
        .timeline-item.right .timeline-dot { left: -8px; }
        .timeline-content { padding: 20px; background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 0.5rem; position: relative; transition: all 0.3s ease; }
        .timeline-content:hover { transform: translateY(-5px); border-color: rgba(0, 160, 233, 0.5); box-shadow: 0 10px 30px rgba(0, 160, 233, 0.1); }
        .timeline-icon { position: absolute; top: 20px; right: 20px; font-size: 1.5rem; color: rgba(0, 160, 233, 0.3); }

        /* --- Enhanced Tools Section Styles --- */
        .tools-category-card {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 1.5rem;
          padding: 2rem 1.5rem;
          position: relative;
          overflow: hidden;
          transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
          cursor: pointer;
        }

        .tools-category-card:hover {
          transform: translateY(-8px) scale(1.02);
          border-color: rgba(0, 160, 233, 0.4);
          box-shadow: 0 25px 50px rgba(0, 160, 233, 0.15), 0 0 0 1px rgba(0, 160, 233, 0.2);
        }

        .tools-category-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, ${secondaryColor}10 0%, transparent 50%, ${secondaryColor}05 100%);
          opacity: 0;
          transition: opacity 0.4s ease;
          z-index: 0;
        }

        .tools-category-card:hover::before {
          opacity: 1;
        }

        .category-header {
          text-align: center;
          margin-bottom: 1.5rem;
          position: relative;
          z-index: 2;
        }

        .category-icon-wrapper {
          width: 60px;
          height: 60px;
          margin: 0 auto 1rem;
          background: linear-gradient(135deg, ${secondaryColor}20, ${secondaryColor}40);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px solid rgba(0, 160, 233, 0.3);
          transition: all 0.3s ease;
        }

        .tools-category-card:hover .category-icon-wrapper {
          transform: scale(1.1) rotate(5deg);
          box-shadow: 0 10px 25px rgba(0, 160, 233, 0.3);
        }

        .category-icon-wrapper i {
          font-size: 1.5rem;
          color: ${secondaryColor};
        }

        .category-title {
          font-size: 1.25rem;
          font-weight: 600;
          color: white;
          margin: 0;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .tools-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 0.75rem;
          position: relative;
          z-index: 2;
        }

        .tool-item {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 0.75rem;
          padding: 0.75rem 1rem;
          text-align: center;
          position: relative;
          overflow: hidden;
          transition: all 0.3s ease;
        }

        .tool-item:hover {
          background: rgba(0, 160, 233, 0.1);
          border-color: rgba(0, 160, 233, 0.3);
          transform: translateY(-2px);
        }

        .tool-name {
          font-size: 0.875rem;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.9);
          position: relative;
          z-index: 2;
        }

        .tool-shine {
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          transition: left 0.5s ease;
        }

        .tool-item:hover .tool-shine {
          left: 100%;
        }

        .floating-particles {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
          z-index: 1;
        }

        .particle {
          position: absolute;
          width: 4px;
          height: 4px;
          background: ${secondaryColor};
          border-radius: 50%;
          opacity: 0.6;
          animation: particleFloat 8s ease-in-out infinite;
        }

        .particle:nth-child(1) {
          top: 20%;
          left: 15%;
        }

        .particle:nth-child(2) {
          top: 60%;
          right: 20%;
        }

        .particle:nth-child(3) {
          bottom: 25%;
          left: 70%;
        }

        @keyframes particleFloat {
          0%, 100% {
            transform: translateY(0px) translateX(0px) scale(1);
            opacity: 0.6;
          }
          25% {
            transform: translateY(-10px) translateX(5px) scale(1.2);
            opacity: 0.8;
          }
          50% {
            transform: translateY(-5px) translateX(-3px) scale(0.8);
            opacity: 0.4;
          }
          75% {
            transform: translateY(-15px) translateX(8px) scale(1.1);
            opacity: 0.7;
          }
        }
        
        /* --- Responsive Styles --- */
        @media (max-width: 991px) {
            .testing-hero { min-height: auto; padding: 5rem 0; }
        }
        @media (max-width: 767px) {
            .timeline-line-new { left: 8px; }
            .timeline-item { width: 100%; padding-left: 60px; padding-right: 15px; }
            .timeline-item.right { left: 0; }
            .timeline-item.left .timeline-dot, .timeline-item.right .timeline-dot { left: 0; }
            .timeline-item.left::after, .timeline-item.right::after { left: 25px; }
        }
      `}</style>
    </div>
  );
};

export default TestingPage;
