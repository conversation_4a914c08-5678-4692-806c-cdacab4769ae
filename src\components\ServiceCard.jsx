import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const ServiceCard = ({ service }) => {
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    // Your existing navigation logic
    if (service.title === 'Internet of Things') navigate('/iot');
    else if (service.title === 'Analytics') navigate('/analytics');
    else if (service.title === 'Artificial Intelligence') navigate('/ai');
    // Add other routes or a generic service page route: navigate(`/services/${service.id}`);
    else console.log(`Clicked on ${service.title}`);
  };

  // Helper to convert hex to rgb for rgba usage
  function hexToRgb(hex) {
    const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
    hex = hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b);
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` : '0,0,0';
  }


  return (
    <div
      className={`card-makonis-glass h-full flex flex-col cursor-pointer transition-all duration-500 ease-out ${
        isHovered ? '-translate-y-4 scale-105' : 'translate-y-0 scale-100'
      }`}
      style={{
        boxShadow: isHovered
          ? `0 25px 50px rgba(0, 0, 0, 0.4), 0 0 0 2px ${service.color || '#00a0e9'}, 0 0 30px rgba(0, 160, 233, 0.3)`
          : '0 10px 25px rgba(0, 0, 0, 0.2)',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
    >
      {/* Animated Background Gradient */}
      <div
        className="absolute inset-0 transition-opacity duration-500 ease-out"
        style={{
          background: `linear-gradient(135deg, ${service.color}15 0%, transparent 50%, ${service.color}10 100%)`,
          opacity: isHovered ? 1 : 0,
          zIndex: 0
        }}
      />

      {/* Floating Particles Effect */}
      {isHovered && (
        <div className="absolute inset-0" style={{ zIndex: 0 }}>
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 rounded-full animate-float opacity-60"
              style={{
                background: service.color || '#00a0e9',
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animationDelay: `${i * 0.2}s`,
              }}
            />
          ))}
        </div>
      )}

      <div className="h-56 overflow-hidden relative rounded-t-3xl">
        <img
          src={service.image}
          alt={service.title}
          className={`w-full h-full object-cover transition-transform duration-500 ease-out ${
            isHovered ? 'scale-110' : 'scale-100'
          }`}
        />

        {/* Enhanced Overlay with Gradient */}
        <div
          className={`absolute inset-0 flex items-end p-4 transition-opacity duration-300 ${
            isHovered ? 'opacity-100' : 'opacity-85'
          }`}
          style={{
            background: `linear-gradient(to bottom, rgba(0,0,0,0.0) 0%, rgba(0,0,0,0.3) 40%, ${service.color || 'rgba(0,0,0,0.8)'} 100%)`
          }}
        >
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-xl bg-white/20 backdrop-blur-sm flex items-center justify-center mr-3 border border-white/30">
              <i
                className={`fas ${service.icon} text-white text-lg`}
                style={{ textShadow: '0 2px 4px rgba(0,0,0,0.3)' }}
              />
            </div>
            <h3 className="text-lg font-bold text-white mb-0 text-shadow">
              {service.title}
            </h3>
          </div>
        </div>

        {/* Shine Effect */}
        <div
          className={`absolute inset-0 transition-transform duration-700 ease-out z-10 ${
            isHovered ? 'translate-x-full' : '-translate-x-full'
          }`}
          style={{
            background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%)',
          }}
        />
      </div>

      <div className="p-7 flex-1 flex flex-col relative z-10">
       

        {/* Service Title */}
        <h4 className="text-xl font-bold mb-3 text-white leading-tight">
          {service.title}
        </h4>

        {/* Service Description */}
        <p className="text-base flex-1 text-white/85 leading-relaxed mb-4">
          {service.description}
        </p>

        {/* Enhanced CTA Section */}
        <div className="mt-auto pt-3 border-t border-white/15 relative">
          {/* Progress Bar Effect */}
          <div
            className={`absolute top-0 left-0 h-0.5 rounded-sm transition-all duration-500 ease-out ${
              isHovered ? 'w-full' : 'w-0'
            }`}
            style={{
              background: `linear-gradient(90deg, ${service.color}, ${service.color}80)`,
            }}
          />

          <div className="flex items-center justify-between">
            <span
              className="font-semibold text-base"
              style={{ color: service.color || '#00a0e9' }}
            >
              Explore Solution
            </span>

            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center border transition-all duration-300 ${
                isHovered ? 'scale-110' : 'scale-100'
              }`}
              style={{
                background: `linear-gradient(135deg, ${service.color}20, ${service.color}40)`,
                borderColor: `${service.color}30`,
              }}
            >
              <i
                className={`fas fa-arrow-right text-sm transition-transform duration-300 ${
                  isHovered ? 'translate-x-1' : 'translate-x-0'
                }`}
                style={{ color: service.color }}
              />
            </div>
          </div>
        </div>
      </div>

    </div>
  );
};

export default ServiceCard;