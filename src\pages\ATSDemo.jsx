import React, { useState, useRef, useEffect } from 'react';
import {
  FaPlay,
  FaPause,
  FaVolumeUp,
  FaVolumeOff,
  FaExpand,
  FaUsers,
  FaChartLine,
  FaClock,
  FaShieldAlt,
  FaRocket,
  FaCheckCircle,
  FaEnvelope,
  FaSearch,
  FaMobileAlt,
  FaPhone,
  FaArrowRight,
  FaFastForward,
  FaFastBackward
} from 'react-icons/fa';
import { gsap } from 'gsap';
import 'bootstrap/dist/css/bootstrap.min.css';
import ATSVideo from '../Asserts/ATS.mp4';

const ATSDemo = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(1); // Keep track of last volume before mute
  const [isVideoLoading, setIsVideoLoading] = useState(true);
  const [videoError, setVideoError] = useState(null);
  const videoRef = useRef(null);
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const ctaRef = useRef(null);
  const visualizationRef = useRef(null);

  const handlePlayPause = async () => {
    if (videoRef.current) {
      try {
        console.log('Video state:', {
          paused: videoRef.current.paused,
          currentTime: videoRef.current.currentTime,
          duration: videoRef.current.duration,
          readyState: videoRef.current.readyState,
          networkState: videoRef.current.networkState
        });

        if (isPlaying || !videoRef.current.paused) {
          videoRef.current.pause();
          setIsPlaying(false);
        } else {
          const playPromise = videoRef.current.play();
          if (playPromise !== undefined) {
            await playPromise;
            setIsPlaying(true);
          }
        }
      } catch (error) {
        console.error('Video playback error:', error);
        setIsPlaying(false);

        // Handle different types of errors
        if (error.name === 'NotAllowedError') {
          alert('Please click the play button to start the video. Autoplay is restricted by your browser.');
        } else if (error.name === 'NotSupportedError') {
          setVideoError('Video format not supported by your browser.');
        } else {
          setVideoError('Failed to play video: ' + error.message);
        }
      }
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
      setIsVideoLoading(false);
      setVideoError(null);
    }
  };

  const handleVideoError = (error) => {
    console.error('Video loading error:', error);
    console.error('Video element error:', videoRef.current?.error);

    let errorMessage = 'Failed to load video. ';
    if (videoRef.current?.error) {
      switch (videoRef.current.error.code) {
        case 1:
          errorMessage += 'Video loading was aborted.';
          break;
        case 2:
          errorMessage += 'Network error occurred.';
          break;
        case 3:
          errorMessage += 'Video format not supported.';
          break;
        case 4:
          errorMessage += 'Video source not found.';
          break;
        default:
          errorMessage += 'Unknown error occurred.';
      }
    } else {
      errorMessage += 'Please check your connection and try again.';
    }

    setVideoError(errorMessage);
    setIsVideoLoading(false);
  };

  const handleCanPlay = () => {
    setIsVideoLoading(false);
    setVideoError(null);
  };

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleProgressClick = (e) => {
    if (videoRef.current && duration) {
      const rect = e.currentTarget.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const clickRatio = clickX / rect.width;
      const newTime = clickRatio * duration;
      videoRef.current.currentTime = newTime;
    }
  };

  const handleVolumeToggle = () => {
    if (videoRef.current) {
      console.log('Volume toggle - Current state:', {
        muted: videoRef.current.muted,
        volume: videoRef.current.volume,
        isMuted: isMuted
      });

      if (isMuted || videoRef.current.muted) {
        videoRef.current.muted = false;
        videoRef.current.volume = volume > 0 ? volume : 0.5; // Restore previous volume or default to 0.5
        setIsMuted(false);
      } else {
        setVolume(videoRef.current.volume || 0.5); // Save current volume before muting
        videoRef.current.muted = true;
        setIsMuted(true);
      }

      console.log('Volume toggle - New state:', {
        muted: videoRef.current.muted,
        volume: videoRef.current.volume,
        isMuted: !isMuted
      });
    }
  };

  const handleFullscreen = () => {
    if (videoRef.current) {
      if (videoRef.current.requestFullscreen) {
        videoRef.current.requestFullscreen();
      } else if (videoRef.current.webkitRequestFullscreen) {
        videoRef.current.webkitRequestFullscreen();
      } else if (videoRef.current.msRequestFullscreen) {
        videoRef.current.msRequestFullscreen();
      }
    }
  };

  const handleSkipForward = () => {
    if (videoRef.current) {
      videoRef.current.currentTime = Math.min(videoRef.current.currentTime + 15, duration);
    }
  };

  const handleSkipBackward = () => {
    if (videoRef.current) {
      videoRef.current.currentTime = Math.max(videoRef.current.currentTime - 15, 0);
    }
  };

  // Video initialization
  useEffect(() => {
    if (videoRef.current) {
      const video = videoRef.current;

      // Set initial volume
      video.volume = 0.5;
      video.muted = false;

      // Add additional event listeners
      const handleLoadStart = () => {
        console.log('Video load started');
        setIsVideoLoading(true);
      };

      const handleCanPlayThrough = () => {
        console.log('Video can play through');
        setIsVideoLoading(false);
      };

      video.addEventListener('loadstart', handleLoadStart);
      video.addEventListener('canplaythrough', handleCanPlayThrough);

      return () => {
        video.removeEventListener('loadstart', handleLoadStart);
        video.removeEventListener('canplaythrough', handleCanPlayThrough);
      };
    }
  }, []);

  // Hero section animations with slide-in effects
  useEffect(() => {
    const ctx = gsap.context(() => {
      const tl = gsap.timeline({ delay: 0.2 });

      // Title slides in from left with rotation
      if (titleRef.current) {
        tl.from(titleRef.current, {
          x: -200,
          opacity: 0,
          rotation: -10,
          duration: 1.5,
          ease: "power3.out"
        });
      }

      // Subtitle slides in from right
      if (subtitleRef.current) {
        tl.from(subtitleRef.current, {
          x: 200,
          opacity: 0,
          duration: 1.2,
          ease: "power2.out"
        }, "-=1");
      }

      // CTA bounces in from bottom
      if (ctaRef.current) {
        tl.from(ctaRef.current, {
          y: 100,
          opacity: 0,
          scale: 0.8,
          duration: 1,
          ease: "bounce.out"
        }, "-=0.8");
      }

      // Visualization rotates in
      if (visualizationRef.current) {
        tl.from(visualizationRef.current, {
          scale: 0,
          rotation: 180,
          opacity: 0,
          duration: 1.5,
          ease: "back.out(1.7)"
        }, "-=1.2");
      }
    }, heroRef);

    return () => ctx.revert();
  }, []);





  return (
    <div className="ats-demo-page">
      <style jsx>{`
        .ats-demo-page {
          overflow-x: hidden;
        }

        .hero-section {
          position: relative;
        }

        .hero-section::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
          opacity: 0.3;
          pointer-events: none;
        }

        /* --- Animations --- */
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes pulse {
            0% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.8;
                box-shadow: 0 0 40px rgba(0, 160, 233, 0.4);
            }
            50% {
                transform: translate(-50%, -50%) scale(1.05);
                opacity: 1;
                box-shadow: 0 0 70px rgba(0, 160, 233, 0.6);
            }
            100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.8;
                box-shadow: 0 0 40px rgba(0, 160, 233, 0.4);
            }
        }

        @keyframes orbit {
          from { transform: translate(-50%, -50%) rotate(0deg) translateX(var(--radius)) rotate(0deg); }
          to { transform: translate(-50%, -50%) rotate(360deg) translateX(var(--radius)) rotate(-360deg); }
        }

        @keyframes dash {
          to { stroke-dashoffset: -20; }
        }

        @keyframes particleFloat {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg);
                opacity: 0.6;
            }
            33% {
                transform: translateY(-20px) translateX(10px) rotate(120deg);
                opacity: 1;
            }
            66% {
                transform: translateY(10px) translateX(-15px) rotate(240deg);
                opacity: 0.8;
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg);
                opacity: 0.7;
            }
            33% {
                transform: translateY(-25px) translateX(15px) rotate(100deg);
                opacity: 1;
            }
            66% {
                transform: translateY(10px) translateX(-20px) rotate(200deg);
                opacity: 0.8;
            }
        }

        /* --- Hero Section Styles --- */
        .hero-section {
          background-attachment: fixed;
          background-size: cover;
          background-position: center;
        }

        .display-1 {
            letter-spacing: -1px;
        }

        .lead {
            font-weight: 300;
        }

        /* Hero stats animation delay */
        .hero-section > .container > .row > .col-lg-6 {
          animation: fadeInUp 0.8s ease-out;
        }

        .hero-section > .container > .row > .col-lg-6:nth-child(2) {
          animation-delay: 0.2s;
        }

        /* --- Video Section Styles --- */
        .video-section {
          padding: 3rem 0;
        }

        .video-section .col-lg-10 {
          animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        /* Ensure video section doesn't overflow */
        .video-section .container {
          max-width: 100%;
          padding: 0 15px;
        }

        .video-container {
            background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
            aspect-ratio: 16/9;
            max-height: 80vh; /* Responsive height constraint */
            width: 100%;
            box-shadow: 0 20px 60px rgba(0, 43, 89, 0.3), 0 8px 25px rgba(0, 157, 230, 0.2);
            border: 2px solid rgba(0, 157, 230, 0.1);
            position: relative;
            overflow: hidden;
        }

        /* Responsive video adjustments */
        @media (max-width: 768px) {
          .video-container {
            max-height: 70vh;
            aspect-ratio: 16/9;
          }
        }

        @media (max-width: 576px) {
          .video-container {
            max-height: 60vh;
            aspect-ratio: 16/9;
          }
        }

        .video-container::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, transparent 30%, rgba(0, 157, 230, 0.05) 50%, transparent 70%);
          pointer-events: none;
          z-index: 1;
        }

        /* Video controls */
        .video-controls {
          opacity: 0;
          transform: translateY(20px);
          transition: all 0.4s ease-out;
          pointer-events: none; /* Allows clicks through when hidden */
          backdrop-filter: blur(15px);
          background: linear-gradient(transparent, rgba(0,0,0,0.9)) !important;
        }

        .video-container:hover .video-controls {
          opacity: 1;
          transform: translateY(0);
          pointer-events: all; /* Enable clicks when visible */
        }

        /* Play/Pause, Skip buttons */
        .video-controls .btn {
            font-size: 1.2rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            width: 45px;
            height: 45px;
            transition: all 0.3s ease;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-controls .btn:hover {
            background: rgba(0, 157, 230, 0.8) !important;
            transform: scale(1.1);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .video-controls .btn:focus {
            box-shadow: 0 0 0 2px rgba(0, 157, 230, 0.5);
            outline: none;
        }

        .video-controls .btn:active {
            transform: scale(0.95);
        }

        /* Progress bar */
        .progress {
            height: 8px !important; /* Make it slightly taller for better clickability */
            cursor: pointer;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            transition: all 0.3s ease-in-out !important; /* Smooth transition for height and scale */
        }

        .progress:hover {
            transform: scaleY(1.5);
        }

        .progress-bar {
            background: linear-gradient(90deg, #002B59 0%, #009DE6 100%) !important;
            border-radius: 3px;
            transition: width 0.1s ease;
        }

        .progress-bar::after { /* Add a subtle glow to the scrubber thumb */
            content: '';
            position: absolute;
            top: 50%;
            left: 100%; /* Relative to the progress-bar itself */
            transform: translate(-50%, -50%);
            width: 12px;
            height: 12px;
            background: #fff;
            box-shadow: 0 0 10px rgba(0, 157, 230, 0.7); /* Subtle blue glow */
            border-radius: 50%;
            opacity: 1; /* Always visible */
            pointer-events: none;
            z-index: 1;
        }

        /* Enhanced Play button overlay */
        .video-play-overlay {
            background: linear-gradient(135deg, rgba(0, 160, 233, 0.95) 0%, rgba(0, 86, 179, 0.95) 100%);
            backdrop-filter: blur(20px);
            border: 3px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                0 0 0 0 rgba(0, 160, 233, 0.7),
                0 25px 50px rgba(0, 43, 89, 0.4),
                0 10px 25px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            width: 100px;
            height: 100px;
            transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
            animation: playButtonPulse 3s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }

        .video-play-overlay::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            transform: rotate(-45deg);
            transition: transform 0.6s ease;
        }

        .video-play-overlay:hover::before {
            transform: rotate(-45deg) translateX(100%);
        }

        .video-play-overlay:hover {
            transform: scale(1.15);
            box-shadow:
                0 0 0 20px rgba(0, 160, 233, 0.2),
                0 30px 60px rgba(0, 43, 89, 0.5),
                0 15px 35px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .video-play-overlay:active {
            transform: scale(1.05);
        }

        /* Play button pulse animation */
        @keyframes playButtonPulse {
            0%, 100% {
                box-shadow:
                    0 0 0 0 rgba(0, 160, 233, 0.7),
                    0 25px 50px rgba(0, 43, 89, 0.4),
                    0 10px 25px rgba(0, 0, 0, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }
            50% {
                box-shadow:
                    0 0 0 15px rgba(0, 160, 233, 0.3),
                    0 25px 50px rgba(0, 43, 89, 0.4),
                    0 10px 25px rgba(0, 0, 0, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }
        }

        /* Pulse rings around play button */
        .play-button-ring {
            position: absolute;
            border: 2px solid rgba(0, 160, 233, 0.4);
            border-radius: 50%;
            animation: playButtonRings 2s ease-out infinite;
        }

        .play-button-ring:nth-child(1) {
            width: 120px;
            height: 120px;
            animation-delay: 0s;
        }

        .play-button-ring:nth-child(2) {
            width: 150px;
            height: 150px;
            animation-delay: 0.5s;
        }

        .play-button-ring:nth-child(3) {
            width: 180px;
            height: 180px;
            animation-delay: 1s;
        }

        @keyframes playButtonRings {
            0% {
                transform: translate(-50%, -50%) scale(0.8);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(1.2);
                opacity: 0;
            }
        }

        /* --- Features Section Styles (Refined) --- */
        .features-section {
          padding-top: 5rem; /* More padding for separation */
          padding-bottom: 5rem; /* More padding for separation */
          background-color: #f8f9fa; /* Light background for contrast */
        }

        .feature-card {
            backdrop-filter: blur(10px); /* If you want this on a lighter background, it might need adjustment or removal */
            background: white;
            border: 1px solid rgba(0, 157, 230, 0.1) !important;
            box-shadow: 0 4px 15px rgba(0, 43, 89, 0.06);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            animation: fadeInUp 0.6s ease-out both;
            padding: 1.5rem; /* Adjust padding for cards */
            border-radius: 0.75rem; /* More rounded corners */
            display: flex; /* Flexbox for internal alignment */
            align-items: center; /* Vertically align icon and text */
            margin-bottom: 1rem; /* Space between stacked cards */
        }

        /* Remove margin-bottom on the last feature card in the list */
        .feature-list-container > div:last-child .feature-card {
            margin-bottom: 0;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0, 43, 89, 0.15), 0 5px 15px rgba(0, 157, 230, 0.1);
            border-color: rgba(0, 157, 230, 0.5) !important;
        }

        .feature-card:nth-child(1) { animation-delay: 0.1s; }
        .feature-card:nth-child(2) { animation-delay: 0.2s; }
        .feature-card:nth-child(3) { animation-delay: 0.3s; }
        .feature-card:nth-child(4) { animation-delay: 0.4s; }

        .feature-icon {
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 157, 230, 0.25);
            width: 55px; /* Slightly larger icon container */
            height: 55px;
            min-width: 55px; /* Prevent shrinking */
            min-height: 55px; /* Prevent shrinking */
            border-radius: 0.5rem; /* Match card radius or slightly less */
            margin-right: 1.25rem; /* More space between icon and text */
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1);
            box-shadow: 0 6px 18px rgba(0, 157, 230, 0.4);
        }

        .feature-card h5 {
            color: #002B59;
            font-size: 1.25rem; /* Slightly larger feature titles */
            margin-bottom: 0.5rem;
        }

        .feature-card p {
            color: #6c757d; /* Clearer muted text color */
            font-size: 1rem;
            line-height: 1.6;
        }

        /* Benefits Card */
        .benefits-card {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #002B59 0%, #009DE6 100%);
            color: white;
            box-shadow: 0 15px 40px rgba(0, 43, 89, 0.3);
            border: none;
            padding: 2.5rem; /* More padding for a richer feel */
            border-radius: 0.75rem; /* Match feature cards */
            height: 100%; /* Ensure it stretches to match the height of feature cards */
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .benefits-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
          opacity: 0.5;
          pointer-events: none;
        }

        .benefits-card h3 {
            font-size: 1.8rem; /* Larger, more prominent heading */
            margin-bottom: 1.5rem; /* More space below heading */
        }

        .benefits-list .fa-check-circle {
            transition: transform 0.2s ease-out;
            color: #00ff88;
            font-size: 1.1rem; /* Slightly larger checkmark icon */
            min-width: 20px; /* Ensure checkmark doesn't push text */
        }

        .benefits-list div:hover .fa-check-circle {
            transform: scale(1.2);
            color: #39ff14 !important;
        }

        .benefits-list span {
            font-size: 1rem; /* Adjust benefit text size */
            line-height: 1.6;
            flex-grow: 1; /* Allow text to take available space */
        }

        /* General Button Hover Effect */
        .btn:hover {
          transform: translateY(-2px);
          transition: transform 0.2s ease;
        }

        /* Responsive Adjustments */
        @media (max-width: 991.98px) { /* For Bootstrap's lg breakpoint and smaller */
            .features-section .row {
                flex-direction: column; /* Stack columns on smaller screens */
            }

            .col-lg-8, .col-lg-4 {
                width: 100%; /* Take full width */
                max-width: 100%; /* Ensure it doesn't overflow */
                margin-bottom: 2rem; /* Add space between stacked sections */
            }

            .benefits-card {
                margin-bottom: 0; /* No extra margin when it's the last element */
            }

            .feature-card {
                margin-bottom: 1.5rem; /* Increase space between feature cards when stacked */
            }
        }

        @media (max-width: 767.98px) { /* For Bootstrap's md breakpoint and smaller */
            .features-section {
                padding-top: 3rem;
                padding-bottom: 3rem;
            }

            .feature-card {
                flex-direction: column; /* Stack icon and text vertically inside card */
                text-align: center;
                padding: 1.25rem;
            }

            .feature-icon {
                margin-right: 0;
                margin-bottom: 1rem; /* Space between stacked icon and text */
            }

            .feature-card h5 {
                font-size: 1.15rem;
            }

            .feature-card p {
                font-size: 0.9rem;
            }

            .benefits-card {
                padding: 2rem;
            }

            .benefits-card h3 {
                font-size: 1.5rem;
            }

            .benefits-list div {
                align-items: flex-start; /* Ensure checkmark stays at the top of the text */
            }
        }

        /* --- New Blue Highlight Boxes --- */
        .highlight-box-blue {
          background: linear-gradient(135deg, #0066cc 0%, #004499 100%);
          border-radius: 12px;
          padding: 1.5rem 1rem;
          text-align: center;
          box-shadow: 0 8px 25px rgba(0, 102, 204, 0.3);
          border: 1px solid rgba(255, 255, 255, 0.1);
          transition: all 0.3s ease;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }

        .highlight-box-blue:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 35px rgba(0, 102, 204, 0.4);
        }

        .highlight-icon-blue {
          width: 50px;
          height: 50px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 0.5rem;
          font-size: 1.5rem;
          color: white;
        }

        /* --- New Demo Features Container --- */
        .demo-features-container {
          padding: 1rem;
        }

        .demo-feature-item-new {
          display: flex;
          align-items: flex-start;
          margin-bottom: 1.5rem;
          padding: 1rem;
          background: rgba(0, 102, 204, 0.05);
          border-radius: 8px;
          border-left: 4px solid #0066cc;
          transition: all 0.3s ease;
        }

        .demo-feature-item-new:hover {
          background: rgba(0, 102, 204, 0.1);
          transform: translateX(5px);
        }

        .demo-feature-icon-new {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #0066cc 0%, #004499 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 1rem;
          flex-shrink: 0;
          color: white;
          font-size: 1.2rem;
        }

        .demo-feature-content {
          flex: 1;
        }

        /* --- Why Choose Cards --- */
        .why-choose-card {
          background: white;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 1.5rem;
          height: 100%;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
        }

        .why-choose-card:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .why-choose-card h6 {
          color: #333;
          line-height: 1.5;
          font-weight: 500;
        }
      `}</style>
      {/* Extraordinary Hero Section */}
      <section
        ref={heroRef}
        className="position-relative overflow-hidden"
        style={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #002956 0%, #001a3a 50%, #000f1f 100%)',
          display: 'flex',
          alignItems: 'center'
        }}
      >
        {/* Animated Background Elements */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {/* Floating Geometric Shapes */}
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="position-absolute"
              style={{
                width: `${60 + Math.random() * 40}px`,
                height: `${60 + Math.random() * 40}px`,
                background: `linear-gradient(45deg, rgba(0, 160, 233, 0.1), rgba(0, 160, 233, 0.3))`,
                borderRadius: Math.random() > 0.5 ? '50%' : '20%',
                top: `${Math.random() * 80 + 10}%`,
                left: `${Math.random() * 80 + 10}%`,
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(0, 160, 233, 0.2)',
                boxShadow: '0 8px 32px rgba(0, 160, 233, 0.1)',
                animation: `float ${3 + Math.random() * 2}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 2}s`
              }}
            />
          ))}

          {/* Floating Particles */}
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="position-absolute"
              style={{
                width: '4px',
                height: '4px',
                background: '#00a0e9',
                borderRadius: '50%',
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                opacity: 0.6,
                boxShadow: '0 0 10px #00a0e9',
                animation: `particleFloat ${4 + Math.random() * 3}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 3}s`
              }}
            />
          ))}

          {/* Gradient Orbs */}
          <div
            className="position-absolute"
            style={{
              width: '300px',
              height: '300px',
              background: 'radial-gradient(circle, rgba(0, 160, 233, 0.15) 0%, transparent 70%)',
              borderRadius: '50%',
              top: '20%',
              right: '10%',
              filter: 'blur(40px)',
              animation: 'floatOrb1 6s ease-in-out infinite'
            }}
          />
          <div
            className="position-absolute"
            style={{
              width: '200px',
              height: '200px',
              background: 'radial-gradient(circle, rgba(0, 160, 233, 0.1) 0%, transparent 70%)',
              borderRadius: '50%',
              bottom: '30%',
              left: '15%',
              filter: 'blur(30px)',
              animation: 'floatOrb2 8s ease-in-out infinite'
            }}
          />
        </div>

        <div className="container position-relative" style={{ zIndex: 2 }}>
          <div className="row align-items-center min-vh-100">
            <div className="col-lg-6 text-white">
              <div className="mb-4">
                <h1
                  ref={titleRef}
                  className="display-1 fw-bold mb-4"
                  style={{
                    fontSize: 'clamp(3rem, 8vw, 4.4rem)',
                    lineHeight: '1.1',
                    background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
                  }}
                >
                  Advanced.<br />
                  <span style={{ color: '#00a0e9' }}>ATS Platform.</span>
                </h1>
              </div>

              <div>
                <p
                  ref={subtitleRef}
                  className="lead mb-5"
                  style={{
                    fontSize: '1.2rem',
                    lineHeight: '1.6',
                    color: 'rgba(255, 255, 255, 0.9)',
                    maxWidth: '600px'
                  }}
                >
                  Experience our revolutionary Applicant Tracking System that transforms
                  recruitment workflows with AI-powered matching, intelligent automation,
                  and comprehensive analytics for faster, smarter hiring decisions.
                </p>
              </div>

              {/* Stats Section */}
              <div ref={ctaRef} className="row mt-5 pt-4">
                {[
                  { number: '50%', label: 'Faster Hiring' },
                  { number: '1000+', label: 'Companies' },
                  { number: '99.9%', label: 'Uptime' }
                ].map((stat, index) => (
                  <div key={index} className="col-4 text-center">
                    <div
                      className="p-3 rounded-4"
                      style={{
                        background: 'rgba(255, 255, 255, 0.05)',
                        backdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        animation: `fadeInUp 0.8s ease-out ${0.6 + index * 0.2}s both`
                      }}
                    >
                      <h3
                        className="fw-bold mb-1"
                        style={{
                          color: '#00a0e9',
                          fontSize: '1.8rem'
                        }}
                      >
                        {stat.number}
                      </h3>
                      <p
                        className="small mb-0"
                        style={{
                          color: 'rgba(255, 255, 255, 0.8)',
                          fontSize: '0.95rem'
                        }}
                      >
                        {stat.label}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="col-lg-6 d-none d-lg-block">
              {/* ATS Visualization */}
              <div
                ref={visualizationRef}
                className="position-relative"
                style={{
                  height: '600px',
                  perspective: '1000px'
                }}
              >
                {/* Central ATS Hub */}
                <div
                  className="position-absolute top-50 start-50 translate-middle"
                  style={{
                    width: '200px',
                    height: '200px',
                    background: 'linear-gradient(135deg, rgba(0, 160, 233, 0.2), rgba(0, 160, 233, 0.4))',
                    borderRadius: '50%',
                    backdropFilter: 'blur(20px)',
                    border: '2px solid rgba(0, 160, 233, 0.3)',
                    boxShadow: '0 0 60px rgba(0, 160, 233, 0.4)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    animation: 'pulse 3s ease-in-out infinite'
                  }}
                >
                  <FaUsers
                    style={{
                      fontSize: '80px',
                      color: '#00a0e9',
                      filter: 'drop-shadow(0 0 20px rgba(0, 160, 233, 0.8))'
                    }}
                  />
                </div>

                {/* Orbiting ATS Features */}
                {[
                  { icon: FaChartLine, angle: 0, radius: 150, color: '#00a0e9' },
                  { icon: FaClock, angle: 60, radius: 180, color: '#0056b3' },
                  { icon: FaShieldAlt, angle: 120, radius: 160, color: '#00a0e9' },
                  { icon: FaRocket, angle: 180, radius: 170, color: '#0056b3' },
                  { icon: FaCheckCircle, angle: 240, radius: 155, color: '#00a0e9' },
                  { icon: FaEnvelope, angle: 300, radius: 175, color: '#0056b3' }
                ].map((item, index) => (
                  <div
                    key={index}
                    className="position-absolute"
                    style={{
                      top: '50%',
                      left: '50%',
                      transform: `translate(-50%, -50%) rotate(${item.angle}deg) translateX(${item.radius}px) rotate(-${item.angle}deg)`,
                      width: '80px',
                      height: '80px',
                      background: `linear-gradient(135deg, ${item.color}20, ${item.color}40)`,
                      borderRadius: '20px',
                      backdropFilter: 'blur(10px)',
                      border: `1px solid ${item.color}30`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: `0 8px 32px ${item.color}20`,
                      animation: `orbit 20s linear infinite`,
                      animationDelay: `${index * -3.33}s`,
                      '--radius': `${item.radius}px` // Pass radius as CSS variable for animation
                    }}
                  >
                    <item.icon
                      style={{
                        fontSize: '24px',
                        color: item.color
                      }}
                    />
                  </div>
                ))}

                {/* Connection Lines */}
                <svg
                  className="position-absolute top-0 start-0 w-100 h-100"
                  style={{ zIndex: -1 }}
                >
                  {[...Array(6)].map((_, i) => (
                    <line
                      key={i}
                      x1="50%"
                      y1="50%"
                      x2={`${50 + 25 * Math.cos(i * Math.PI / 3)}%`}
                      y2={`${50 + 25 * Math.sin(i * Math.PI / 3)}%`}
                      stroke="rgba(0, 160, 233, 0.3)"
                      strokeWidth="2"
                      strokeDasharray="5,5"
                      style={{
                        animation: `dash 2s linear infinite`,
                        animationDelay: `${i * 0.3}s`
                      }}
                    />
                  ))}
                </svg>
              </div>
            </div>
          </div>
        </div>
      </section>

       <section className="live-demo-section section-padding">
        <div className="container">
          <div className="text-center">
            <h1  style={{
                    fontSize: 'clamp(3rem, 8vw, 4rem)',
                    lineHeight: '1.1',
                    background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
                  }} >Live ATS Platform Demonstration</h1>
            <p className="text-white/80 mt-3 mb-5"
            style={{
              fontSize: '1.2rem',
              lineHeight: '1.6',
              maxWidth: '1200px',
              margin: '0 auto',
              opacity: 0.9
            }}>
              Experience a complete walkthrough of our Applicant Tracking
              System. This demo showcases real-world scenarios including
              candidate sourcing, interview scheduling, collaborative hiring,
              and advanced reporting features that drive recruitment success.
            </p>
          </div>

          <div className="row mb-5 justify-content-center">
            <div className="col-md-2 col-sm-6 mb-3">
              <div className="highlight-box-blue">
                <div className="highlight-icon-blue">
                  <FaRocket />
                </div>
                <h6 className="text-white mt-2 mb-0">Real-Time Collaboration</h6>
              </div>
            </div>
            <div className="col-md-2 col-sm-6 mb-3">
              <div className="highlight-box-blue">
                <div className="highlight-icon-blue">
                  <FaChartLine />
                </div>
                <h6 className="text-white mt-2 mb-0">Advanced Analytics</h6>
              </div>
            </div>
            <div className="col-md-2 col-sm-6 mb-3">
              <div className="highlight-box-blue">
                <div className="highlight-icon-blue">
                  <FaSearch />
                </div>
                <h6 className="text-white mt-2 mb-0">AI-Powered Matching</h6>
              </div>
            </div>
            <div className="col-md-2 col-sm-6 mb-3">
              <div className="highlight-box-blue">
                <div className="highlight-icon-blue">
                  <FaMobileAlt />
                </div>
                <h6 className="text-white mt-2 mb-0">Mobile Optimized</h6>
              </div>
            </div>
          </div>

          <div className="row video-area align-items-center">
            <div className="col-lg-8">
              <div className="video-container">
                <video
                  ref={videoRef}
                  className="w-100 h-100"
                  onTimeUpdate={handleTimeUpdate}
                  onLoadedMetadata={handleLoadedMetadata}
                  onCanPlay={handleCanPlay}
                  onError={handleVideoError}
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                  onEnded={() => setIsPlaying(false)}
                  poster="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2015&q=80"
                  style={{
                    objectFit: 'contain',
                    maxHeight: '100%',
                    width: '100%'
                  }}
                  preload="metadata"
                  playsInline
                >
                  <source src={ATSVideo} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>

                {isVideoLoading && (
                  <div className="position-absolute top-50 start-50 translate-middle text-white">
                    Loading...
                  </div>
                )}
                {videoError && (
                  <div className="position-absolute top-50 start-50 translate-middle text-danger bg-light p-3 rounded">
                    {videoError}
                  </div>
                )}

                {!isPlaying && !isVideoLoading && !videoError && (
                  <div className="video-play-overlay" onClick={handlePlayPause}>
                    <FaPlay
                      size={30}
                      color="#fff"
                      style={{ marginLeft: "5px" }}
                    />
                  </div>
                )}

                <div className="video-controls">
                  <div className="d-flex align-items-center text-white">
                    <button
                      className="btn btn-link text-white fs-5 p-0 me-2"
                      onClick={handleSkipBackward}
                      title="Skip backward 15s"
                    >
                      <FaFastBackward />
                    </button>
                    <button
                      className="btn btn-link text-white fs-5 p-0 me-3"
                      onClick={handlePlayPause}
                      title={isPlaying ? "Pause" : "Play"}
                    >
                      {isPlaying ? <FaPause /> : <FaPlay />}
                    </button>
                    <button
                      className="btn btn-link text-white fs-5 p-0 me-3"
                      onClick={handleSkipForward}
                      title="Skip forward 15s"
                    >
                      <FaFastForward />
                    </button>
                    <div className="mx-3 flex-grow-1">
                      <div className="progress" onClick={handleProgressClick} style={{ cursor: 'pointer' }}>
                        <div
                          className="progress-bar"
                          role="progressbar"
                          style={{
                            width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%`,
                          }}
                        ></div>
                      </div>
                    </div>
                    <span className="me-3">
                      {formatTime(currentTime || 0)} / {formatTime(duration || 0)}
                    </span>
                    <button
                      className="btn btn-link text-white fs-5 p-0 me-2"
                      onClick={handleVolumeToggle}
                      title={isMuted ? "Unmute" : "Mute"}
                    >
                      {isMuted ? <FaVolumeOff /> : <FaVolumeUp />}
                    </button>
                    <button
                      className="btn btn-link text-white fs-5 p-0"
                      onClick={handleFullscreen}
                      title="Fullscreen"
                    >
                      <FaExpand />
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-4 mt-4 mt-lg-0">
              <div className="demo-features-container">
                <div className="demo-feature-item-new">
                  <div className="demo-feature-icon-new">
                    <FaUsers />
                  </div>
                  <div className="demo-feature-content">
                    <h6 className="mb-1">Candidate Management</h6>
                    <p className="mb-0 text-white/85 small">Streamline your hiring process with comprehensive tracking tools</p>
                  </div>
                </div>
                <div className="demo-feature-item-new">
                  <div className="demo-feature-icon-new">
                    <FaChartLine />
                  </div>
                  <div className="demo-feature-content">
                    <h6 className="mb-1">Analytics & Reporting</h6>
                    <p className="mb-0 text-white/85 small">Get detailed insights and make data-driven hiring decisions</p>
                  </div>
                </div>
                <div className="demo-feature-item-new">
                  <div className="demo-feature-icon-new">
                    <FaClock />
                  </div>
                  <div className="demo-feature-content">
                    <h6 className="mb-1">Time-to-Hire Optimization</h6>
                    <p className="mb-0 text-white/85 small">Reduce hiring time by 50% with automated workflows</p>
                  </div>
                </div>
                <div className="demo-feature-item-new">
                  <div className="demo-feature-icon-new">
                    <FaShieldAlt />
                  </div>
                  <div className="demo-feature-content">
                    <h6 className="mb-1">Secure & Compliant</h6>
                    <p className="mb-0 text-white/85 small">Enterprise-grade security with GDPR compliance</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Our ATS? Section */}
      <section className="why-choose-us-section section-padding">
        <div className="container">
          <div className="text-center">
            <h2 >Why Choose Our ATS?</h2>
          </div>
          <div className="row mt-5">
            <div className="col-md-4 mb-4">
              <div className="why-choose-card">
                <h6 className="mb-3">Streamline your hiring process with comprehensive candidate tracking and management tools that save time.</h6>
              </div>
            </div>
            <div className="col-md-4 mb-4">
              <div className="why-choose-card">
                <h6 className="mb-3">Streamline your hiring process with comprehensive candidate tracking and management tools that save time.</h6>
              </div>
            </div>
            <div className="col-md-4 mb-4">
              <div className="why-choose-card">
                <h6 className="mb-3">Streamline your hiring process with comprehensive candidate tracking and management tools that save time.</h6>
              </div>
            </div>
            <div className="col-md-4 mb-4">
              <div className="why-choose-card">
                <h6 className="mb-3">Streamline your hiring process with comprehensive candidate tracking and management tools that save time.</h6>
              </div>
            </div>
            <div className="col-md-4 mb-4">
              <div className="why-choose-card">
                <h6 className="mb-3">Streamline your hiring process with comprehensive candidate tracking and management tools that save time.</h6>
              </div>
            </div>
            <div className="col-md-4 mb-4">
              <div className="why-choose-card">
                <h6 className="mb-3">Streamline your hiring process with comprehensive candidate tracking and management tools that save time.</h6>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ATSDemo;