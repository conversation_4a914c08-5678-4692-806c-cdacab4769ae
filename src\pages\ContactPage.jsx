import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from "react-leaflet";
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import "@fortawesome/fontawesome-free/css/all.min.css";

// --- Map Change Component ---
const ChangeMapView = ({ coords, zoom = 6 }) => {
  const map = useMap();
  useEffect(() => {
    if (coords) {
      map.flyTo(coords, zoom, {
        animate: true,
        duration: 1.5,
      });
    }
  }, [coords, zoom, map]);
  return null;
};

// --- Main Contact Page Component ---
const ContactPage = () => {
  const [activeTab, setActiveTab] = useState("bangalore");
  const mapRef = useRef(null);

  // Updated locations array with regions
  const locations = [
    {
      id: "bangalore",
      title: "Bangalore",
      fullTitle: "Bangalore Office",
      address: "51, 3rd Cross Rd, Aswath Nagar, Marathahalli, Bengaluru, Karnataka 560037",
      phone: "+91 8041707838",
      email: "<EMAIL>",
      directionsUrl: "https://www.google.com/maps/dir/?api=1&destination=12.958057,77.702026",
      coords: [12.958057, 77.702026],
      region: "asia"
    },
    {
      id: "hyderabad",
      title: "Hyderabad",
      fullTitle: "Hyderabad Office",
      address: "5th Floor, Modern Profound Tech Park, HITEC City, Kondapur, Telangana 500081",
      phone: "+91 8041707838",
      email: "<EMAIL>",
      directionsUrl: "https://www.google.com/maps/dir/?api=1&destination=17.4593,78.3659",
      coords: [17.4593, 78.3659],
      region: "asia"
    },
    {
      id: "vijayawada",
      title: "Vijayawada",
      fullTitle: "Vijayawada Office",
      address: "71-3-8A, Koneru vari St, Patamata, Benz Circle, Vijayawada, Andhra Pradesh 520010",
      phone: "+91 8041707838",
      email: "<EMAIL>",
      directionsUrl: "https://www.google.com/maps/dir/?api=1&destination=16.4952,80.6496",
      coords: [16.4952, 80.6496],
      region: "asia"
    },
    {
      id: "texas",
      title: "Texas",
      fullTitle: "Texas Office",
      address: "Suite -410 Office – T Kings Plaza 14111 King Rd Frisco TX 75036",
      phone: "****** 525 8121",
      email: "<EMAIL>",
      directionsUrl: "https://www.google.com/maps/dir/?api=1&destination=33.1497,-96.7972",
      coords: [33.1497, -96.7972],
      region: "america"
    },
    {
      id: "melbourne",
      title: "Melbourne",
      fullTitle: "Melbourne Office",
      address: "54. Mansfield ST Berwick VIC 3806",
      phone: "+61 3 9707 1122",
      email: "<EMAIL>",
      directionsUrl: "https://www.google.com/maps/dir/?api=1&destination=-38.0322,145.3473",
      coords: [-38.0322, 145.3473],
      region: "oceania"
    },
    {
      id: "ontario",
      title: "Ontario",
      fullTitle: "Ontario Office",
      address: "4503 Glen Erin Dr., Mississauga, ON, Canada L5M 4G5",
      phone: "****** 828 2222",
      email: "<EMAIL>",
      directionsUrl: "https://www.google.com/maps/dir/?api=1&destination=43.5674,-79.7072",
      coords: [43.5674, -79.7072],
      region: "america"
    },
  ];

  const currentOffice = locations.find((loc) => loc.id === activeTab);

  // Custom icon for the main, interactive map markers
  const customMarkerIcon = new L.Icon({
    iconUrl: 'data:image/svg+xml;base64,' + btoa(`
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="32" height="32">
        <path fill="#00a0e9" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
        <circle cx="12" cy="9" r="2" fill="#002956"/>
      </svg>
    `),
    iconSize: [32, 32],
    iconAnchor: [16, 32],
    popupAnchor: [0, -32],
  });

  // --- NEW: Custom icon for the top overview map ---
  const overviewMarkerIcon = new L.Icon({
    iconUrl: 'data:image/svg+xml;base64,' + btoa(`
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18">
        <circle cx="12" cy="12" r="9" fill="#00a0e9" stroke="#ffffff" stroke-width="2"/>
      </svg>
    `),
    iconSize: [18, 18],
    iconAnchor: [9, 9],
  });

  // Map control functions
  const zoomIn = () => mapRef.current?.zoomIn();
  const zoomOut = () => mapRef.current?.zoomOut();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-makonis-primary to-gray-800">
      <div className="container-makonis section-padding">
        {/* Header Section */}
        <div className="text-center mb-8 lg:mb-16">
          <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-4 lg:mb-6">
            Our Global Presence
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed px-4">
            Connect with us at any of our strategically located offices around the world.
            We're here to serve you with excellence and innovation.
          </p>
        </div>

        {/* --- NEW: World Map Overview Section --- */}
        <div className="mb-8 lg:mb-12 card-makonis-glass p-4">
            <MapContainer
                center={[25, 20]}
                zoom={2}
                style={{ height: '450px', width: '100%' }}
                className="rounded-xl leaflet-map-container"
                zoomControl={false}
                scrollWheelZoom={false}
                dragging={false}
                doubleClickZoom={false}
                attributionControl={false}
                touchZoom={false}
            >
                <TileLayer
                    url="https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png"
                />
                {locations.map(loc => (
                    <Marker key={`overview-${loc.id}`} position={loc.coords} icon={overviewMarkerIcon}>
                       <Popup className="custom-popup">
                         <div className="p-1">
                           <h4 className="font-semibold text-white text-center">{loc.title}</h4>
                         </div>
                       </Popup>
                    </Marker>
                ))}
            </MapContainer>
        </div>


        {/* --- Existing Layout: Interactive Map and Details --- */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-8">
          {/* Map Section */}
          <div className="lg:col-span-8">
            <div className="card-makonis-glass p-4 lg:p-6 h-full">
              <div className="map-wrapper relative">
                <MapContainer
                  center={[20, 0]}
                  zoom={2}
                  style={{ height: "500px", width: "100%" }}
                  className="leaflet-map-container rounded-2xl overflow-hidden shadow-makonis-lg"
                  zoomControl={false}
                  ref={mapRef}
                >
                  <TileLayer
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                    attribution='© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                  />

                  {locations.map(loc => (
                    <Marker key={loc.id} position={loc.coords} icon={customMarkerIcon}>
                      <Popup className="custom-popup">
                        <div className="p-2">
                          <h4 className="font-semibold text-white mb-1">{loc.fullTitle}</h4>
                          <p className="text-sm text-gray-300">{loc.address}</p>
                        </div>
                      </Popup>
                    </Marker>
                  ))}

                  {currentOffice && <ChangeMapView coords={currentOffice.coords} zoom={8} />}
                </MapContainer>

                {/* Zoom Controls Only */}
                <div className="absolute bottom-4 right-4 z-[1000] flex gap-1">
                  <button
                    onClick={zoomIn}
                    className="w-8 h-8 bg-white/90 backdrop-blur-sm text-gray-700 hover:bg-white border border-gray-200 hover:border-makonis-secondary rounded-md flex items-center justify-center text-sm font-bold transition-all duration-300 hover:scale-110"
                  >
                    +
                  </button>
                  <button
                    onClick={zoomOut}
                    className="w-8 h-8 bg-white/90 backdrop-blur-sm text-gray-700 hover:bg-white border border-gray-200 hover:border-makonis-secondary rounded-md flex items-center justify-center text-sm font-bold transition-all duration-300 hover:scale-110"
                  >
                    −
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column: Location Selector and Details */}
          <div className="lg:col-span-4 flex flex-col" style={{ height: "500px" }}>
            {/* Location Selector */}
            <div className="card-makonis-glass p-3 lg:p-4 mb-4">
              <h3 className="text-lg lg:text-xl font-semibold text-white mb-3 lg:mb-4">Select Office</h3>
              <div className="grid grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-2">
                {locations.map((location) => (
                  <button
                    key={location.id}
                    className={`px-2 py-1.5 lg:px-3 lg:py-2 rounded-lg font-medium transition-all duration-300 text-xs lg:text-sm ${
                      activeTab === location.id
                        ? 'bg-makonis-gradient text-white shadow-glow transform scale-105'
                        : 'bg-white/10 text-gray-300 hover:bg-white/20 hover:text-white border border-white/20'
                    }`}
                    onClick={() => setActiveTab(location.id)}
                  >
                    {location.title}
                  </button>
                ))}
              </div>
            </div>

            {/* Office Details */}
            {currentOffice && (
              <div className="card-makonis-glass p-3 lg:p-4 flex-1 animate-fade-in">
                <div className="flex items-center gap-2 lg:gap-3 mb-3 lg:mb-2">
                  <div className="w-8 h-8 lg:w-10 lg:h-10 bg-makonis-gradient rounded-lg flex items-center justify-center">
                    <i className="fas fa-building text-white text-xs lg:text-sm"></i>
                  </div>
                  <h3 className="text-base lg:text-lg font-semibold text-white">{currentOffice.fullTitle}</h3>
                </div>

                <div className="space-y-3 lg:space-y-4">
                  <div className="flex items-start gap-2 lg:gap-3">
                    <div className="w-6 h-6 lg:w-8 lg:h-8 bg-makonis-secondary/20 rounded-md flex items-center justify-center flex-shrink-0 mt-0.5">
                      <i className="fas fa-map-marker-alt text-makonis-secondary text-xs lg:text-sm"></i>
                    </div>
                    <div>
                      <span className="text-xs font-medium text-makonis-secondary uppercase tracking-wide">Address</span>
                      <p className="text-gray-300 mt-0.5 leading-relaxed text-xs lg:text-sm">{currentOffice.address}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2 lg:gap-3">
                    <div className="w-6 h-6 lg:w-8 lg:h-8 bg-makonis-secondary/20 rounded-md flex items-center justify-center flex-shrink-0">
                      <i className="fas fa-phone text-makonis-secondary text-xs lg:text-sm"></i>
                    </div>
                    <div>
                      <span className="text-xs font-medium text-makonis-secondary uppercase tracking-wide">Phone</span>
                      <a
                        href={`tel:${currentOffice.phone}`}
                        className="block text-white hover:text-makonis-secondary transition-colors duration-300 mt-0.5 text-xs lg:text-sm"
                      >
                        {currentOffice.phone}
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start gap-2 lg:gap-3">
                    <div className="w-6 h-6 lg:w-8 lg:h-8 bg-makonis-secondary/20 rounded-md flex items-center justify-center flex-shrink-0">
                      <i className="fas fa-envelope text-makonis-secondary text-xs lg:text-sm"></i>
                    </div>
                    <div>
                      <span className="text-xs font-medium text-makonis-secondary uppercase tracking-wide">Email</span>
                      <a
                        href={`mailto:${currentOffice.email}`}
                        className="block text-white hover:text-makonis-secondary transition-colors duration-300 mt-0.5 text-xs lg:text-sm"
                      >
                        {currentOffice.email}
                      </a>
                    </div>
                  </div>

                  <a
                    href={currentOffice.directionsUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-makonis-primary w-full text-center inline-flex items-center justify-center gap-2 mt-3 lg:mt-4 text-xs lg:text-sm py-2"
                  >
                    <i className="fas fa-directions text-xs lg:text-sm"></i>
                    Get Directions
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Custom Leaflet Styles */}
      <style>{`
        .leaflet-popup-content-wrapper, .leaflet-popup-tip {
          background: rgba(0, 41, 86, 0.95) !important;
          color: #fff !important;
          border-radius: 12px !important;
          border: 1px solid rgba(0, 160, 233, 0.3) !important;
          backdrop-filter: blur(10px) !important;
        }

        .leaflet-popup-content {
          margin: 8px 12px !important;
        }

        .leaflet-popup-content a {
          color: #00a0e9 !important;
        }

        .leaflet-popup-close-button {
          color: #fff !important;
          font-size: 18px !important;
          padding: 4px 8px !important;
        }

        .leaflet-popup-close-button:hover {
          color: #00a0e9 !important;
        }

        .custom-popup .leaflet-popup-content-wrapper {
          background: rgba(0, 41, 86, 0.95) !important;
        }

        /* Responsive adjustments */
        @media (max-width: 1024px) {
          .leaflet-map-container {
            height: 450px !important;
          }
          .lg\\:col-span-4 {
            height: 450px !important;
          }
        }

        @media (max-width: 768px) {
          .leaflet-map-container {
            height: 350px !important;
          }
          .lg\\:col-span-4 {
            height: auto !important;
          }
        }

        @media (max-width: 640px) {
          .leaflet-map-container {
            height: 300px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default ContactPage;