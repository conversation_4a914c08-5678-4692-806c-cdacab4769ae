import React, { useState, useEffect } from "react";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa";

const ClientTestimonials = () => {
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      role: "President & CEO, Ansrsource",
      review:
        "<PERSON><PERSON><PERSON> consistently stands out because they genuinely listen. They understand your business's current state and challenges, then expertly guide you to talent that fits both your capability needs and your organizational culture. I've partnered with <PERSON><PERSON> and his team for years and consider them invaluable to my organization's success.",
    },
    {
      name: "<PERSON><PERSON>",
      role: "COO, Redwood Software Inc.",
      review:
        "We highly recommend Makonis to any employer looking for hiring top talent. They are professional, genuine and highly invested in finding you the best talent. We can count on them in finding right talent within time and budget.",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Head of Engineering, LoanIQ",
      review:
        "Taking over Finastra's LoanIQ engineering in 2019, I found traditional hiring too slow. Makonis quickly supplied quality contract-to-hire engineers for testing and programming, even boosting their training investment. Our collaboration has been very positive; I wish them ongoing success.",
    },
    {
      name: "<PERSON><PERSON>",
      role: "VP, Tech at FinPoint",
      review:
        "Exceptional service! Their speed, accuracy, and client support have consistently exceeded our expectations. Their dedication and detail orientation in all hiring cycles have made a real difference to our productivity and onboarding experience.",
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  // Auto-advance testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  // Helper function to get testimonials for display (previous, current, next)
  const getDisplayTestimonials = () => {
    const prevIndex = (currentIndex - 1 + testimonials.length) % testimonials.length;
    const nextIndex = (currentIndex + 1) % testimonials.length;

    return [
      { ...testimonials[prevIndex], position: 'left' },
      { ...testimonials[currentIndex], position: 'center' },
      { ...testimonials[nextIndex], position: 'right' }
    ];
  };

  const displayTestimonials = getDisplayTestimonials();

  return (
    <div
      style={{
        background: "linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%)",
        minHeight: "100vh",
        padding: "60px 20px",
        position: "relative",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      {/* Title */}
      <h2
        style={{
          color: "white",
          fontSize: "36px",
          fontWeight: "bold",
          textAlign: "center",
          marginBottom: "60px",
          letterSpacing: "1px",
        }}
      >
        What Our Clients Say
      </h2>

      {/* Navigation Arrows */}
      <button
        onClick={prevTestimonial}
        style={{
          position: "absolute",
          left: "40px",
          top: "50%",
          transform: "translateY(-50%)",
          background: "rgba(255, 255, 255, 0.1)",
          border: "2px solid rgba(255, 255, 255, 0.3)",
          borderRadius: "50%",
          width: "60px",
          height: "60px",
          color: "white",
          fontSize: "20px",
          cursor: "pointer",
          transition: "all 0.3s ease",
          zIndex: 10,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
        onMouseEnter={(e) => {
          e.target.style.background = "rgba(255, 255, 255, 0.2)";
          e.target.style.borderColor = "rgba(255, 255, 255, 0.5)";
        }}
        onMouseLeave={(e) => {
          e.target.style.background = "rgba(255, 255, 255, 0.1)";
          e.target.style.borderColor = "rgba(255, 255, 255, 0.3)";
        }}
      >
        <FaArrowLeft />
      </button>

      <button
        onClick={nextTestimonial}
        style={{
          position: "absolute",
          right: "40px",
          top: "50%",
          transform: "translateY(-50%)",
          background: "rgba(255, 255, 255, 0.1)",
          border: "2px solid rgba(255, 255, 255, 0.3)",
          borderRadius: "50%",
          width: "60px",
          height: "60px",
          color: "white",
          fontSize: "20px",
          cursor: "pointer",
          transition: "all 0.3s ease",
          zIndex: 10,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
        onMouseEnter={(e) => {
          e.target.style.background = "rgba(255, 255, 255, 0.2)";
          e.target.style.borderColor = "rgba(255, 255, 255, 0.5)";
        }}
        onMouseLeave={(e) => {
          e.target.style.background = "rgba(255, 255, 255, 0.1)";
          e.target.style.borderColor = "rgba(255, 255, 255, 0.3)";
        }}
      >
        <FaArrowRight />
      </button>

      {/* Three Testimonial Cards Container */}
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          gap: "30px",
          maxWidth: "1200px",
          width: "100%",
          position: "relative",
        }}
      >
        {displayTestimonials.map((testimonial, index) => {
          const isCenter = testimonial.position === 'center';
          const isLeft = testimonial.position === 'left';
          const isRight = testimonial.position === 'right';

          return (
            <div
              key={`${testimonial.name}-${testimonial.position}`}
              style={{
                background: "#ffffff",
                borderRadius: "16px",
                padding: isCenter ? "40px 30px" : "30px 25px",
                width: isCenter ? "400px" : "320px",
                minHeight: isCenter ? "300px" : "280px",
                boxShadow: isCenter
                  ? "0 20px 40px rgba(0, 0, 0, 0.3)"
                  : "0 10px 20px rgba(0, 0, 0, 0.2)",
                transform: isCenter
                  ? "scale(1.05)"
                  : isLeft
                    ? "scale(0.9) translateX(20px)"
                    : "scale(0.9) translateX(-20px)",
                opacity: isCenter ? 1 : 0.7,
                transition: "all 0.5s ease",
                position: "relative",
                zIndex: isCenter ? 3 : 1,
                cursor: isCenter ? "default" : "pointer",
              }}
              onClick={() => {
                if (!isCenter) {
                  if (isLeft) {
                    prevTestimonial();
                  } else {
                    nextTestimonial();
                  }
                }
              }}
            >
              {/* Quote Text */}
              <div
                style={{
                  fontSize: isCenter ? "16px" : "14px",
                  lineHeight: "1.5",
                  color: "#333333",
                  fontStyle: "italic",
                  marginBottom: "25px",
                  textAlign: "left",
                  fontWeight: "400",
                  display: "-webkit-box",
                  WebkitBoxOrient: "vertical",
                  WebkitLineClamp: isCenter ? 6 : 5,
                  overflow: "hidden",
                }}
              >
                <span
                  style={{
                    fontSize: isCenter ? "20px" : "16px",
                    color: "#666666",
                    marginRight: "6px",
                  }}
                >
                  "
                </span>
                {testimonial.review}
                <span
                  style={{
                    fontSize: isCenter ? "20px" : "16px",
                    color: "#666666",
                    marginLeft: "6px",
                  }}
                >
                  "
                </span>
              </div>

              {/* Company Logo */}
              <div
                style={{
                  textAlign: "center",
                  marginBottom: "20px",
                }}
              >
                <div
                  style={{
                    width: isCenter ? "60px" : "50px",
                    height: isCenter ? "60px" : "50px",
                    background: "#FF6B94",
                    borderRadius: "8px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    margin: "0 auto",
                    boxShadow: "0 4px 12px rgba(255, 107, 148, 0.3)",
                  }}
                >
                  <div
                    style={{
                      width: "0",
                      height: "0",
                      borderLeft: isCenter ? "12px solid transparent" : "10px solid transparent",
                      borderRight: isCenter ? "12px solid transparent" : "10px solid transparent",
                      borderBottom: isCenter ? "20px solid white" : "16px solid white",
                      marginTop: "-3px",
                    }}
                  ></div>
                </div>
              </div>

              {/* Name and Title */}
              <div style={{ textAlign: "center" }}>
                <h4
                  style={{
                    fontSize: isCenter ? "18px" : "16px",
                    fontWeight: "600",
                    color: "#333333",
                    marginBottom: "6px",
                    letterSpacing: "0.3px",
                  }}
                >
                  {testimonial.name}
                </h4>
                <p
                  style={{
                    fontSize: isCenter ? "14px" : "12px",
                    color: "#666666",
                    margin: "0",
                    fontWeight: "400",
                  }}
                >
                  {testimonial.role}
                </p>
              </div>
            </div>
          );
        })}
      {/* Dots Indicator */}
      <div
        style={{
          marginTop: "50px",
          display: "flex",
          gap: "12px",
          justifyContent: "center",
        }}
      >
        {testimonials.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            style={{
              width: "12px",
              height: "12px",
              borderRadius: "50%",
              border: "none",
              background: index === currentIndex ? "#ffffff" : "rgba(255, 255, 255, 0.4)",
              cursor: "pointer",
              transition: "all 0.3s ease",
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default ClientTestimonials;