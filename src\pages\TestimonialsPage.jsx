import React, { useEffect, useState, useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
 
const ClientTestimonials = () => {
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      role: "President & CEO, Ansrsource",
      photo: "https://ansrsource.com/wp-content/uploads/2022/06/Headshots-1.png",
      logo: "https://ansrsource.com/wp-content/uploads/elementor/thumbs/ansrsource-with-tagline-original_break-02-new-qum3i8u2w81sqeo0fbu6mizgjoynke3fwt9pdk1avs.png",
      review:
        "<PERSON><PERSON><PERSON> consistently stands out because they genuinely listen. They understand your business's current state and challenges, then expertly guide you to talent that fits both your capability needs and your organizational culture. I've partnered with <PERSON><PERSON> and his team for years and consider them invaluable to my organization's success.",
    },
    {
      name: "<PERSON><PERSON> <PERSON><PERSON>",
      role: "COO, Redwood Software Inc.",
      photo: "https://cdn.theorg.com/8589a507-b587-45d7-bd11-ee05879622d3_thumb.jpg",
      logo: "https://www.redwood.com/wp-content/uploads/Redwood_Logo_White.svg",
      review:
        "We highly recommend Makonis to any employer looking for hiring top talent. They are professional, genuine and highly invested in finding you the best talent. We can count on them in finding right talent within time and budget.",
    },
    {
      name: "Sudhakar Krishnamachari",
      role: "Head of Engineering, LoanIQ",
      photo: "https://randomuser.me/api/portraits/men/40.jpg",
      logo: "https://upload.wikimedia.org/wikipedia/commons/3/3e/Panasonic_logo_blue.svg",
      review:
        "Taking over Finastra's LoanIQ engineering in 2019, I found traditional hiring too slow. Makonis quickly supplied quality contract-to-hire engineers for testing and programming, even boosting their training investment. Our collaboration has been very positive; I wish them ongoing success.",
    },
    {
      name: "Priya Sharma",
      role: "VP, Tech at FinPoint",
      photo: "https://randomuser.me/api/portraits/women/44.jpg",
      logo: "https://upload.wikimedia.org/wikipedia/commons/f/fb/Placeholder_company_logo.svg",
      review:
        "Exceptional service! Their speed, accuracy, and client support have consistently exceeded our expectations. Their dedication and detail orientation in all hiring cycles have made a real difference to our productivity and onboarding experience.",
    },
  ];
 
  const [expandedIndex, _setExpandedIndex] = useState(null);
  const expandedIndexRef = useRef(expandedIndex);
  const swiperRef = useRef(null);
  const timeoutRef = useRef(null);
 
  const setExpandedIndex = (value) => {
    expandedIndexRef.current = value;
    _setExpandedIndex(value);
  };
 
  useEffect(() => {
    if (expandedIndex !== null) {
      swiperRef.current?.autoplay?.stop();
      clearTimeout(timeoutRef.current);
      timeoutRef.current = setTimeout(() => {
        setExpandedIndex(null);
        swiperRef.current?.autoplay?.start();
      }, 60000);
    }
    return () => clearTimeout(timeoutRef.current);
  }, [expandedIndex]);
 
  const handleReadToggle = (index) => {
    clearTimeout(timeoutRef.current);
    if (expandedIndexRef.current === index) {
      setExpandedIndex(null);
      timeoutRef.current = setTimeout(() => {
        if (expandedIndexRef.current === null && swiperRef.current?.autoplay) {
          swiperRef.current.autoplay.start();
        }
      }, 3000);
    } else {
      setExpandedIndex(index);
    }
  };
 
  return (
    <div
      className="container-fluid py-5"
      style={{
        background: "#0c1f38",
        minHeight: "100vh",
        paddingTop: "120px",
        paddingBottom: "100px",
        overflowX: "hidden",
      }}
    >
      <h2
        className="text-center mb-4"
        style={{
          color: "#ffffff",
          fontWeight: "800",
          fontSize: "2.8rem",
          paddingTop: "30px",
        }}
      >
        What Our Clients Say
      </h2>
      <div
        style={{
          width: "60px",
          height: "4px",
          backgroundColor: "#00A0E9",
          margin: "0 auto 24px auto",
          borderRadius: "10px",
        }}
      ></div>
 
      <Swiper
        onSwiper={(swiper) => (swiperRef.current = swiper)}
        spaceBetween={20}
        loop={true}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
        }}
        speed={800}
        modules={[Autoplay]}
        breakpoints={{
          0: { slidesPerView: 1.05 },
          576: { slidesPerView: 1.2 },
          768: { slidesPerView: 2 },
          992: { slidesPerView: 2.5 },
          1200: { slidesPerView: 3 },
        }}
        style={{ paddingBottom: "60px", paddingLeft: "15px" }}
        pagination={{
          clickable: true,
        }}
        className="custom-swiper pt-5"
      >
        {testimonials.map((item, index) => (
          <SwiperSlide key={index} style={{ display: "flex", justifyContent: "center" }}>
            <div
              style={{
                background: "#ffffff",
                borderRadius: "16px",
                borderTop: "4px solid #00A0E9",
                padding: "16px 16px 12px",
                maxWidth: "340px",
                minHeight: "260px",
                width: "100%",
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                boxShadow: "0 0 12px rgba(0,160,233,0.25)",
              }}
            >
              <div>
                <blockquote
                  style={{
                    borderLeft: "4px solid #00A0E9",
                    paddingLeft: "14px",
                    color: "#0a1992",
                    fontSize: "0.95rem",
                    fontWeight: "500",
                    fontStyle: "italic",
                    lineHeight: "1.6",
                    display: "-webkit-box",
                    WebkitBoxOrient: "vertical",
                    WebkitLineClamp:
                      expandedIndex === index
                        ? "unset"
                        : item.name === "Sudhakar Krishnamachari"
                        ? 4
                        : 5,
                    overflow: "hidden",
                    maxHeight:
                      expandedIndex === index
                        ? "none"
                        : item.name === "Sudhakar Krishnamachari"
                        ? "calc(1.6em * 4)"
                        : "calc(1.6em * 5)",
                    marginBottom: "0",
                    position: "relative",
                  }}
                >
                  <span
                    style={{
                      fontSize: "20px",
                      color: "#00A0E9",
                      marginRight: "6px",
                    }}
                  >
                    “
                  </span>
                  {item.review}
                  <span
                    style={{
                      fontSize: "20px",
                      color: "#00A0E9",
                      marginLeft: "6px",
                    }}
                  >
                    ”
                  </span>
                </blockquote>
 
                {item.review.length > 150 && (
                  <span
                    onClick={() => handleReadToggle(index)}
                    style={{
                      color: "#0055cc",
                      fontWeight: "bold",
                      cursor: "pointer",
                      fontSize: "0.85rem",
                      marginTop: "6px",
                      display: "inline-block",
                    }}
                  >
                    {expandedIndex === index ? "show less" : "...read more"}
                  </span>
                )}
              </div>
 
              <div style={{ marginTop: "12px" }}>
                <div
                  style={{
                    fontSize: "16px",
                    color: "#FFD700",
                    marginBottom: "10px",
                    lineHeight: "1",
                  }}
                >
                  ★★★★★
                </div>
 
                <div className="d-flex justify-content-between align-items-center">
                  <div className="d-flex align-items-center gap-2">
                    <img
                      src={item.photo}
                      alt={item.name}
                      style={{
                        width: "42px",
                        height: "42px",
                        borderRadius: "50%",
                        border: "2px solid #1a6eff",
                        objectFit: "cover",
                      }}
                    />
                    <div>
                      <h6
                        className="mb-0 fw-bold"
                        style={{ color: "#0a1992", fontSize: "0.95rem" }}
                      >
                        {item.name}
                      </h6>
                      <small
                        style={{ color: "#0a1992bb", fontSize: "0.8rem" }}
                      >
                        {item.role}
                      </small>
                    </div>
                  </div>
                  <img
                    src={item.logo}
                    alt="company"
                    style={{
                      height: "32px",
                      maxWidth: "80px",
                      objectFit: "contain",
                    }}
                  />
                </div>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
 
      <style>
        {`
          .custom-swiper .swiper-pagination {
            text-align: center;
            margin-top: 80px;
          }
        `}
      </style>
    </div>
  );
};
 
export default ClientTestimonials;