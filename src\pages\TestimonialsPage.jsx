import React, { useState, useEffect } from "react";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa";

const ClientTestimonials = () => {
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      role: "President & CEO, Ansrsource",
      review:
        "<PERSON><PERSON><PERSON> consistently stands out because they genuinely listen. They understand your business's current state and challenges, then expertly guide you to talent that fits both your capability needs and your organizational culture. I've partnered with <PERSON><PERSON> and his team for years and consider them invaluable to my organization's success.",
    },
    {
      name: "<PERSON><PERSON>",
      role: "COO, Redwood Software Inc.",
      review:
        "We highly recommend Makonis to any employer looking for hiring top talent. They are professional, genuine and highly invested in finding you the best talent. We can count on them in finding right talent within time and budget.",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Head of Engineering, LoanIQ",
      review:
        "Taking over Finastra's LoanIQ engineering in 2019, I found traditional hiring too slow. Makonis quickly supplied quality contract-to-hire engineers for testing and programming, even boosting their training investment. Our collaboration has been very positive; I wish them ongoing success.",
    },
    {
      name: "<PERSON><PERSON>",
      role: "VP, Tech at FinPoint",
      review:
        "Exceptional service! Their speed, accuracy, and client support have consistently exceeded our expectations. Their dedication and detail orientation in all hiring cycles have made a real difference to our productivity and onboarding experience.",
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  // Auto-advance testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const currentTestimonial = testimonials[currentIndex];

  return (
    <div
      className="container-fluid d-flex align-items-center justify-content-center"
      style={{
        background: "linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%)",
        minHeight: "100vh",
        padding: "40px 20px",
        position: "relative",
      }}
    >
      {/* Navigation Arrows */}
      <button
        onClick={prevTestimonial}
        style={{
          position: "absolute",
          left: "20px",
          top: "50%",
          transform: "translateY(-50%)",
          background: "rgba(255, 255, 255, 0.1)",
          border: "none",
          borderRadius: "50%",
          width: "50px",
          height: "50px",
          color: "white",
          fontSize: "18px",
          cursor: "pointer",
          transition: "all 0.3s ease",
          zIndex: 10,
        }}
        onMouseEnter={(e) => {
          e.target.style.background = "rgba(255, 255, 255, 0.2)";
          e.target.style.transform = "translateY(-50%) scale(1.1)";
        }}
        onMouseLeave={(e) => {
          e.target.style.background = "rgba(255, 255, 255, 0.1)";
          e.target.style.transform = "translateY(-50%) scale(1)";
        }}
      >
        <FaArrowLeft />
      </button>

      <button
        onClick={nextTestimonial}
        style={{
          position: "absolute",
          right: "20px",
          top: "50%",
          transform: "translateY(-50%)",
          background: "rgba(255, 255, 255, 0.1)",
          border: "none",
          borderRadius: "50%",
          width: "50px",
          height: "50px",
          color: "white",
          fontSize: "18px",
          cursor: "pointer",
          transition: "all 0.3s ease",
          zIndex: 10,
        }}
        onMouseEnter={(e) => {
          e.target.style.background = "rgba(255, 255, 255, 0.2)";
          e.target.style.transform = "translateY(-50%) scale(1.1)";
        }}
        onMouseLeave={(e) => {
          e.target.style.background = "rgba(255, 255, 255, 0.1)";
          e.target.style.transform = "translateY(-50%) scale(1)";
        }}
      >
        <FaArrowRight />
      </button>

      {/* Main Testimonial Card */}
      <div
        style={{
          background: "#ffffff",
          borderRadius: "24px",
          padding: "60px 50px",
          maxWidth: "600px",
          width: "100%",
          margin: "0 auto",
          boxShadow: "0 20px 60px rgba(0, 0, 0, 0.3)",
          position: "relative",
          transition: "all 0.5s ease",
        }}
      >
        {/* Quote Text */}
        <div
          style={{
            fontSize: "20px",
            lineHeight: "1.6",
            color: "#333333",
            fontStyle: "italic",
            marginBottom: "40px",
            textAlign: "left",
            fontWeight: "400",
          }}
        >
          <span
            style={{
              fontSize: "24px",
              color: "#666666",
              marginRight: "8px",
            }}
          >
            "
          </span>
          {currentTestimonial.review}
          <span
            style={{
              fontSize: "24px",
              color: "#666666",
              marginLeft: "8px",
            }}
          >
            "
          </span>
        </div>

        {/* Divider Line */}
        <div
          style={{
            width: "100%",
            height: "1px",
            background: "linear-gradient(to right, transparent, #e0e0e0, transparent)",
            margin: "40px 0",
          }}
        ></div>

        {/* Company Logo */}
        <div
          style={{
            textAlign: "center",
            marginBottom: "40px",
          }}
        >
          <div
            style={{
              width: "80px",
              height: "80px",
              background: "#FF6B94",
              borderRadius: "12px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              margin: "0 auto",
              boxShadow: "0 8px 20px rgba(255, 107, 148, 0.3)",
            }}
          >
            <div
              style={{
                width: "0",
                height: "0",
                borderLeft: "15px solid transparent",
                borderRight: "15px solid transparent",
                borderBottom: "25px solid white",
                marginTop: "-5px",
              }}
            ></div>
          </div>
        </div>

        {/* Name and Title */}
        <div style={{ textAlign: "center" }}>
          <h3
            style={{
              fontSize: "22px",
              fontWeight: "600",
              color: "#333333",
              marginBottom: "8px",
              letterSpacing: "0.5px",
            }}
          >
            {currentTestimonial.name}
          </h3>
          <p
            style={{
              fontSize: "16px",
              color: "#666666",
              margin: "0",
              fontWeight: "400",
            }}
          >
            {currentTestimonial.role}
          </p>
        </div>
      </div>

      {/* Dots Indicator */}
      <div
        style={{
          position: "absolute",
          bottom: "40px",
          left: "50%",
          transform: "translateX(-50%)",
          display: "flex",
          gap: "12px",
        }}
      >
        {testimonials.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            style={{
              width: "12px",
              height: "12px",
              borderRadius: "50%",
              border: "none",
              background: index === currentIndex ? "#ffffff" : "rgba(255, 255, 255, 0.4)",
              cursor: "pointer",
              transition: "all 0.3s ease",
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default ClientTestimonials;